import React from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { DiarioOficialCPF } from "../../model/DiariosOficiaisCPF";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { parseValue, translatePropToLabel } from "../../helpers";
import { useReportActions, useReportMode } from "../../context/ReportContext";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";

export function useRenderDiariosOficiaisCPFArray(
  sectionTitle: string
): ArrayRenderStrategy<DiarioOficialCPF> {
  const actions = useReportActions();
  const mode = useReportMode();

  const shouldInclude = (isDeleted: boolean) => {
    switch (mode) {
      case "trash":
        return isDeleted;
      case "print-pdf":
      case undefined:
      default:
        return !isDeleted;
    }
  };

  const testEntryDeleted = (entry: any): boolean => {
    const isLocalDeleted = entry.local?.is_deleted === true;
    const areDetalhesDeleted = entry.detalhes
      ? Object.values(entry.detalhes).every((v: any) => v.is_deleted === true)
      : false;
    const isDescricaoDeleted = entry["descrição"]?.is_deleted === true;
    const isTextoDeleted = entry["texto correspondente"]?.is_deleted === true;

    return isLocalDeleted && areDetalhesDeleted && isDescricaoDeleted && isTextoDeleted;
  };

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted);

  const formatByKey: Record<
    string,
    (entry?: DiarioOficialCPF) => React.ReactElement | null
  > = {
    local: (entry) => {
      if (!entry?.local || !shouldInclude(entry.local.is_deleted || false)) return null;

      const onToggleField = () => {
        const updater = actions.updateSectionEntries;
        if (!updater) return;

        updater(
          sectionTitle,
          (e: any) => {
            if (e === entry && e.local) {
              e.local.is_deleted = !e.local.is_deleted;
            }
          },
          (e: any) => e.local?.is_deleted === true,
          testSectionDeleted
        );
      };

      return (
        <CustomGridContainer cols={3}>
          <CustomGridItem
            cols={1}
            className="mb-6"
            onToggleField={onToggleField}
          >
            <CustomReadOnlyInputField
              label={entry.local.label.toUpperCase()}
              colorClass="bg-primary"
              value={String(entry.local.value)}
              tooltip={renderSourceTooltip(entry.local.source)}
            />
          </CustomGridItem>
        </CustomGridContainer>
      );
    },

    detalhes: (entry) => {
      if (!entry?.detalhes) return null;
      const subs = Object.entries(entry.detalhes).filter(([, v]) =>
        shouldInclude((v as any).is_deleted)
      );
      if (!subs.length) return null;

      return (
        <CustomGridContainer
          cols={2}
          columnFirst
          className="mb-6"
        >
          {subs.map(([fieldKey, val]) => {
            const onToggleField = () => {
              const updater = actions.updateSectionEntries;
              if (!updater) return;

              updater(
                sectionTitle,
                (e: any) => {
                  if (e === entry) {
                    const d = e.detalhes?.[fieldKey];
                    if (d) d.is_deleted = !d.is_deleted;
                  }
                },
                (e: any) => e.detalhes ? Object.values(e.detalhes).every((v: any) => v.is_deleted === true) : false,
                testSectionDeleted
              );
            };

            return (
              <CustomGridItem
                key={fieldKey}
                cols={1}
                onToggleField={onToggleField}
              >
                <CustomReadOnlyInputField
                  label={translatePropToLabel((val as any).label).toUpperCase()}
                  value={parseValue(String((val as any).value))}
                  tooltip={renderSourceTooltip((val as any).source)}
                />
              </CustomGridItem>
            );
          })}
        </CustomGridContainer>
      );
    },

    ["descrição"]: (entry) => {
      if (!entry?.["descrição"] || !shouldInclude(entry["descrição"].is_deleted || false)) return null;

      const onToggleField = () => {
        const updater = actions.updateSectionEntries;
        if (!updater) return;

        updater(
          sectionTitle,
          (e: any) => {
            if (e === entry && e["descrição"]) {
              e["descrição"].is_deleted = !e["descrição"].is_deleted;
            }
          },
          (e: any) => e["descrição"]?.is_deleted === true,
          testSectionDeleted
        );
      };

      return (
        <CustomGridContainer
          cols={1}
          gap="sm"
          className="mb-6"
        >
          <CustomGridItem
            cols={1}
            onToggleField={onToggleField}
          >
            <CustomReadOnlyInputField
              label={translatePropToLabel(entry["descrição"].label).toUpperCase()}
              element="textarea"
              value={String(entry["descrição"].value)}
              icon={<MdOutlineSubdirectoryArrowRight size={16} />}
              tooltip={renderSourceTooltip(entry["descrição"].source)}
            />
          </CustomGridItem>
        </CustomGridContainer>
      );
    },

    ["texto correspondente"]: (entry) => {
      if (!entry?.["texto correspondente"] || !shouldInclude(entry["texto correspondente"].is_deleted || false)) return null;

      const onToggleField = () => {
        const updater = actions.updateSectionEntries;
        if (!updater) return;

        updater(
          sectionTitle,
          (e: any) => {
            if (e === entry && e["texto correspondente"]) {
              e["texto correspondente"].is_deleted = !e["texto correspondente"].is_deleted;
            }
          },
          (e: any) => e["texto correspondente"]?.is_deleted === true,
          testSectionDeleted
        );
      };

      return (
        <CustomGridContainer
          cols={1}
          gap="sm"
          className="mb-6"
        >
          <CustomGridItem
            cols={1}
            onToggleField={onToggleField}
          >
            <CustomReadOnlyInputField
              label={translatePropToLabel(entry["texto correspondente"].label).toUpperCase()}
              element="textarea"
              value={String(entry["texto correspondente"].value)}
              icon={<MdOutlineSubdirectoryArrowRight size={16} />}
              tooltip={renderSourceTooltip(entry["texto correspondente"].source)}
            />
          </CustomGridItem>
        </CustomGridContainer>
      );
    },
  };

  const validateKeys = (keys: Array<keyof DiarioOficialCPF>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (entry: DiarioOficialCPF): React.ReactElement[] => {
    const keys = Object.keys(entry) as Array<keyof DiarioOficialCPF>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Diários Oficiais CPF] Chaves inválidas:", keys);
    }

    return keys
      .map((chave) => formatByKey[chave]?.(entry))
      .filter((el): el is React.ReactElement => el !== null);
  };

  const render = (dataArray: DiarioOficialCPF[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Diários Oficiais CPF] Expected array but received:", typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      if (mode === "trash") {
        return entry.local?.is_deleted === true ||
          (entry.detalhes && Object.values(entry.detalhes).some((v: any) => v.is_deleted === true)) ||
          entry["descrição"]?.is_deleted === true ||
          entry["texto correspondente"]?.is_deleted === true;
      } else {
        const isDeleted = testEntryDeleted(entry);
        return !isDeleted;
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach((entry, index) => {
      const elements = renderSingleItem(entry);

      if (filteredData.length > 1) {
        allElements.push(
          <div key={`diario-${index}`} className="mb-4">
            {elements}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
  };
}
