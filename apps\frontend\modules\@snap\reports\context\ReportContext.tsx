import React, { createContext, useContext, PropsWithChildren, useMemo } from "react";
import { ReportSection, ReportMetadata } from "../global";
import { Draft } from "immer";

// --- Section State Context ---
const SectionsContext = createContext<ReportSection[]>([]);

// --- Deleted Sections State Context ---
const DeletedSectionsContext = createContext<ReportSection[] | undefined>(undefined);

// --- Metadata State Context ---
const MetadataContext = createContext<ReportMetadata | undefined>(undefined);

// --- Report Type State Context ---
const ReportTypeContext = createContext<string>("");

// --- Is Trash Enabled Context ---
const IsTrashEnabledContext = createContext<boolean>(false);

// --- Is Print Enabled Context ---
const IsPrintEnabledContext = createContext<boolean>(false);

// --- Profile Image State Context ---
const ProfileImageContext = createContext<string | undefined>(undefined);

// --- Mode Context ---
export type RenderMode = 'default' | 'trash' | 'print-pdf';
const ModeContext = createContext<RenderMode>('default');

// --- Save State Context ---
const IsSavingContext = createContext<boolean>(false);

// --- Unified Actions Context ---
export interface ReportActions {
  setReportSections: (sections: ReportSection[]) => void;
  isPendingSave?: () => React.RefObject<boolean>;
  setDeletedSections?: (sections: ReportSection[]) => void;
  setMetadata: (metadata: ReportMetadata) => void;
  setReportType: (type: string) => void;
  setProfileImage?: (image: string) => void;
  updateSectionEntries?: {
    // Assinatura original (sem índice)
    (
      sectionTitle: string,
      updaterFn: (entry: Draft<ReportSection["data"][0]>) => void,
      testEntryDeletedFn: (entry: ReportSection["data"][0]) => boolean,
      testSectionDeletedFn: (section: ReportSection) => boolean
    ): void;
    // Nova assinatura (com índice)
    (
      sectionTitle: string,
      updaterFn: (entry: Draft<ReportSection["data"][0]>, index: number) => void,
      testEntryDeletedFn: (entry: ReportSection["data"][0]) => boolean,
      testSectionDeletedFn: (section: ReportSection) => boolean
    ): void;
  };
}
const ActionsContext = createContext<ReportActions | undefined>(undefined);

interface ProviderProps {
  sections: ReportSection[];
  metadata: ReportMetadata;
  reportType: string;
  isTrashEnabled: boolean,
  isPrintEnabled: boolean,
  isSaving?: boolean;
  image?: string;
  actions: ReportActions;
  renderMode: RenderMode;
}

export function ReportProvider({
  children,
  sections,
  metadata,
  reportType,
  isTrashEnabled,
  isPrintEnabled,
  isSaving = false,
  image,
  actions,
  renderMode,
}: PropsWithChildren<ProviderProps>) {
  // Memoize each state slice separately
  const sectionsValue = useMemo(() => sections, [sections]);
  const metadataValue = useMemo(() => metadata, [metadata]);
  const reportTypeValue = useMemo(() => reportType, [reportType]);
  const isTrashEnabledValue = useMemo(() => isTrashEnabled, [isTrashEnabled]);
  const isPrintEnabledValue = useMemo(() => isPrintEnabled, [isPrintEnabled]);
  const isSavingValue = useMemo(() => isSaving, [isSaving]);
  const profileImageValue = useMemo(() => image, [image]);
  const modeValue = renderMode;

  // Memoize all actions together
  const actionsValue = useMemo(() => actions, [
    actions.setReportSections,
    actions.setDeletedSections,
    actions.setMetadata,
    actions.setReportType,
    actions.setProfileImage,
    actions.updateSectionEntries,
    actions.isPendingSave
  ]);

  return (
    <SectionsContext.Provider value={sectionsValue}>
      <MetadataContext.Provider value={metadataValue}>
        <IsTrashEnabledContext.Provider value={isTrashEnabledValue}>
          <IsPrintEnabledContext.Provider value={isPrintEnabledValue}>
            <IsSavingContext.Provider value={isSavingValue}>
              <ReportTypeContext.Provider value={reportTypeValue}>
                <ProfileImageContext.Provider value={profileImageValue}>
                  <ModeContext.Provider value={modeValue}>
                    <ActionsContext.Provider value={actionsValue}>
                      {children}
                    </ActionsContext.Provider>
                  </ModeContext.Provider>
                </ProfileImageContext.Provider>
              </ReportTypeContext.Provider>
            </IsSavingContext.Provider>
          </IsPrintEnabledContext.Provider>
        </IsTrashEnabledContext.Provider>
      </MetadataContext.Provider>
    </SectionsContext.Provider>
  );
}

// --- Hooks for state consumption ---
export function useReportSections() {
  const ctx = useContext(SectionsContext);
  if (ctx === undefined)
    throw new Error("useReportSections must be used within ReportProvider");
  return ctx;
}

export function useDeletedSections() {
  return useContext(DeletedSectionsContext) ?? [];
}

export function useReportMetadata() {
  const ctx = useContext(MetadataContext);
  if (!ctx)
    throw new Error("useReportMetadata must be used within ReportProvider");
  return ctx;
}

export function useReportType() {
  return useContext(ReportTypeContext);
}

export function useIsTrashEnabled() {
  return useContext(IsTrashEnabledContext);
}

export function useIsPrintEnabled() {
  return useContext(IsPrintEnabledContext);
}

export function useIsSaving() {
  return useContext(IsSavingContext);
}

export function useProfileImage() {
  return useContext(ProfileImageContext);
}

export function useReportMode(): RenderMode {
  return useContext(ModeContext);
}

// --- Unified hook for actions ---
export function useReportActions(): ReportActions {
  const ctx = useContext(ActionsContext);
  if (!ctx)
    throw new Error("useReportActions must be used within ReportProvider");
  return ctx;
}

/**
 * Walk a value tree looking for any nested `is_deleted === true`.
 * Only looks at objects and arrays; primitives are ignored.
 */
function hasAnyDeleted(obj: any): boolean {
  if (obj && typeof obj === "object") {
    // If this object itself is a deleted‐flag container, pick it up.
    if (obj.is_deleted === true) return true;

    // Otherwise, check all its properties or array entries
    return Object.values(obj).some((v) => hasAnyDeleted(v));
  }
  return false;
}

export function useVisibleReportSections() {
  const sections = useContext(SectionsContext);
  const mode = useContext(ModeContext);

  if (sections === undefined) {
    throw new Error("useVisibleReportSections must be inside ReportProvider");
  }

  return useMemo(() => {
    if (mode === "trash") {
      // show sections that have _any_ deleted flag, or
      // were deleted as a whole
      return sections.filter(
        (sec) => sec.is_deleted === true || hasAnyDeleted(sec.data)
      );
    } else {
      // default & print-pdf: only fully non-deleted sections
      return sections.filter((sec) => sec.is_deleted !== true);
    }
  }, [sections, mode]);
}